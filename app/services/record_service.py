"""
入住记录服务层
"""
from typing import List, Optional
from datetime import date, datetime
from sqlalchemy.orm import Session
from app.models.record import ResidenceRecord
from app.repositories.record_repo import RecordRepository
from app.repositories.resident_repo import ResidentRepository
from app.repositories.dormitory_repo import DormitoryRepository
from app.schemas.record import RecordCreate, RecordUpdate, RecordResponse, RecordFilter
from app.core.logging import logger


class RecordService:
    """入住记录服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.repo = RecordRepository(db)
        self.resident_repo = ResidentRepository(db)
        self.dormitory_repo = DormitoryRepository(db)
    
    def get_records(
        self, 
        skip: int = 0, 
        limit: int = 100,
        filters: RecordFilter = None
    ) -> List[RecordResponse]:
        """获取入住记录列表"""
        try:
            records = self.repo.get_records_with_details(skip, limit)
            
            # 应用筛选条件
            if filters:
                if filters.dormitory_id:
                    records = [r for r in records if r.dormitory_id == filters.dormitory_id]
                if filters.department_id:
                    records = [r for r in records if r.resident.department_id == filters.department_id]
                if filters.status:
                    records = [r for r in records if r.status == filters.status]
                if filters.check_in_start:
                    records = [r for r in records if r.check_in_date >= filters.check_in_start]
                if filters.check_in_end:
                    records = [r for r in records if r.check_in_date <= filters.check_in_end]
            
            # 转换为响应模式
            result = []
            for record in records:
                # 计算已住天数
                days_stayed = self._calculate_days_stayed(record.check_in_date, record.check_out_date)
                
                # 手动构建字典
                record_dict = {
                    "id": record.id,
                    "resident_id": record.resident_id,
                    "dormitory_id": record.dormitory_id,
                    "bed_number": record.bed_number,
                    "check_in_date": record.check_in_date,
                    "check_out_date": record.check_out_date,
                    "status": record.status,
                    "notes": record.notes,
                    "created_at": record.created_at,
                    "updated_at": record.updated_at,
                    "resident_name": record.resident.name if record.resident else None,
                    "dormitory_name": record.dormitory.name if record.dormitory else None,
                    "department_name": record.resident.department.name if record.resident and record.resident.department else None,
                    "days_stayed": days_stayed
                }
                result.append(RecordResponse(**record_dict))
            
            logger.info(f"获取入住记录列表成功，共{len(result)}条记录")
            return result
            
        except Exception as e:
            logger.error(f"获取入住记录列表失败: {str(e)}")
            raise
    
    def get_record(self, record_id: str) -> Optional[RecordResponse]:
        """获取单个入住记录"""
        try:
            record = self.repo.get_by_id(record_id)
            if not record:
                return None
            
            # 计算已住天数
            days_stayed = self._calculate_days_stayed(record.check_in_date, record.check_out_date)
            
            # 手动构建字典
            record_dict = {
                "id": record.id,
                "resident_id": record.resident_id,
                "dormitory_id": record.dormitory_id,
                "bed_number": record.bed_number,
                "check_in_date": record.check_in_date,
                "check_out_date": record.check_out_date,
                "status": record.status,
                "notes": record.notes,
                "created_at": record.created_at,
                "updated_at": record.updated_at,
                "resident_name": record.resident.name if record.resident else None,
                "dormitory_name": record.dormitory.name if record.dormitory else None,
                "department_name": record.resident.department.name if record.resident and record.resident.department else None,
                "days_stayed": days_stayed
            }
            
            logger.info(f"获取入住记录成功: {record.id}")
            return RecordResponse(**record_dict)
            
        except Exception as e:
            logger.error(f"获取入住记录失败: {str(e)}")
            raise
    
    def create_record(self, record_data: RecordCreate) -> RecordResponse:
        """创建入住记录"""
        try:
            # 验证住户是否存在
            resident = self.resident_repo.get_by_id(record_data.resident_id)
            if not resident:
                raise ValueError("指定的住户不存在")

            # 验证宿舍是否存在
            dormitory = self.dormitory_repo.get_by_id(record_data.dormitory_id)
            if not dormitory:
                raise ValueError("指定的宿舍不存在")

            # 检查住户是否已有活跃的入住记录
            current_record = self.repo.get_resident_current_record(record_data.resident_id)
            if current_record:
                raise ValueError("住户已有活跃的入住记录，请先办理离开手续")

            # 自动分配床位号（如果未指定）
            if record_data.bed_number is None:
                # 获取可用床位列表
                available_beds = self.dormitory_repo.get_available_beds(
                    record_data.dormitory_id,
                    record_data.check_in_date
                )
                if not available_beds:
                    raise ValueError("该宿舍没有可用床位")

                # 分配第一个可用床位
                assigned_bed_number = available_beds[0]
            else:
                # 使用指定的床位号，但需要验证
                if record_data.bed_number > dormitory.total_beds:
                    raise ValueError(f"床位号不能超过宿舍总床位数({dormitory.total_beds})")

                # 检查床位是否有冲突
                if self.repo.check_bed_conflict(
                    record_data.dormitory_id,
                    record_data.bed_number,
                    record_data.check_in_date
                ):
                    raise ValueError("该床位在指定日期已被占用")

                assigned_bed_number = record_data.bed_number
            
            # 创建入住记录
            record_dict = record_data.model_dump()
            record_dict["bed_number"] = assigned_bed_number  # 使用分配的床位号
            record_dict["status"] = "ACTIVE"
            record = self.repo.create(record_dict)
            
            # 计算已住天数
            days_stayed = self._calculate_days_stayed(record.check_in_date, record.check_out_date)
            
            # 手动构建响应字典
            response_dict = {
                "id": record.id,
                "resident_id": record.resident_id,
                "dormitory_id": record.dormitory_id,
                "bed_number": record.bed_number,
                "check_in_date": record.check_in_date,
                "check_out_date": record.check_out_date,
                "status": record.status,
                "notes": record.notes,
                "created_at": record.created_at,
                "updated_at": record.updated_at,
                "resident_name": resident.name,
                "dormitory_name": dormitory.name,
                "department_name": resident.department.name if resident.department else None,
                "days_stayed": days_stayed
            }
            result = RecordResponse(**response_dict)
            
            logger.info(f"创建入住记录成功: {resident.name} -> {dormitory.name}")
            return result
            
        except Exception as e:
            logger.error(f"创建入住记录失败: {str(e)}")
            raise
    
    def update_record(self, record_id: str, record_data: RecordUpdate) -> Optional[RecordResponse]:
        """更新入住记录"""
        try:
            record = self.repo.get_by_id(record_id)
            if not record:
                return None
            
            # 验证离开日期
            if record_data.check_out_date:
                if record_data.check_out_date < record.check_in_date:
                    raise ValueError("离开日期不能早于入住日期")
                
                # 如果设置了离开日期，状态应该改为COMPLETED
                if record_data.status is None:
                    record_data.status = "COMPLETED"
            
            # 更新记录
            update_data = {k: v for k, v in record_data.model_dump().items() if v is not None}
            updated_record = self.repo.update(record_id, update_data)
            
            # 计算已住天数
            days_stayed = self._calculate_days_stayed(updated_record.check_in_date, updated_record.check_out_date)
            
            # 手动构建响应字典
            response_dict = {
                "id": updated_record.id,
                "resident_id": updated_record.resident_id,
                "dormitory_id": updated_record.dormitory_id,
                "bed_number": updated_record.bed_number,
                "check_in_date": updated_record.check_in_date,
                "check_out_date": updated_record.check_out_date,
                "status": updated_record.status,
                "notes": updated_record.notes,
                "created_at": updated_record.created_at,
                "updated_at": updated_record.updated_at,
                "resident_name": updated_record.resident.name if updated_record.resident else None,
                "dormitory_name": updated_record.dormitory.name if updated_record.dormitory else None,
                "department_name": updated_record.resident.department.name if updated_record.resident and updated_record.resident.department else None,
                "days_stayed": days_stayed
            }
            result = RecordResponse(**response_dict)
            
            logger.info(f"更新入住记录成功: {record_id}")
            return result
            
        except Exception as e:
            logger.error(f"更新入住记录失败: {str(e)}")
            raise
    
    def delete_record(self, record_id: str) -> bool:
        """删除入住记录"""
        try:
            record = self.repo.get_by_id(record_id)
            if not record:
                return False
            
            # 允许删除任何状态的记录
            # 如果是活跃记录，删除时会自动释放床位
            
            success = self.repo.delete(record_id)
            
            if success:
                logger.info(f"删除入住记录成功: {record_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"删除入住记录失败: {str(e)}")
            raise
    
    def checkout_resident(self, record_id: str, checkout_date: date, notes: str = None) -> Optional[RecordResponse]:
        """办理住户离开"""
        try:
            record = self.repo.get_by_id(record_id)
            if not record:
                return None
            
            if record.status != "ACTIVE":
                raise ValueError("只能为活跃状态的入住记录办理离开")
            
            if checkout_date < record.check_in_date:
                raise ValueError("离开日期不能早于入住日期")
            
            # 更新记录
            update_data = {
                "check_out_date": checkout_date,
                "status": "COMPLETED",
                "notes": notes or record.notes
            }
            
            updated_record = self.repo.update(record_id, update_data)
            
            # 计算已住天数
            days_stayed = self._calculate_days_stayed(updated_record.check_in_date, updated_record.check_out_date)
            
            # 手动构建响应字典
            response_dict = {
                "id": updated_record.id,
                "resident_id": updated_record.resident_id,
                "dormitory_id": updated_record.dormitory_id,
                "bed_number": updated_record.bed_number,
                "check_in_date": updated_record.check_in_date,
                "check_out_date": updated_record.check_out_date,
                "status": updated_record.status,
                "notes": updated_record.notes,
                "created_at": updated_record.created_at,
                "updated_at": updated_record.updated_at,
                "resident_name": updated_record.resident.name if updated_record.resident else None,
                "dormitory_name": updated_record.dormitory.name if updated_record.dormitory else None,
                "department_name": updated_record.resident.department.name if updated_record.resident and updated_record.resident.department else None,
                "days_stayed": days_stayed
            }
            result = RecordResponse(**response_dict)
            
            logger.info(f"办理住户离开成功: {updated_record.resident.name}")
            return result
            
        except Exception as e:
            logger.error(f"办理住户离开失败: {str(e)}")
            raise
    
    def get_active_records(self) -> List[RecordResponse]:
        """获取所有活跃的入住记录"""
        try:
            records = self.repo.get_active_records()
            
            result = []
            for record in records:
                days_stayed = self._calculate_days_stayed(record.check_in_date, record.check_out_date)
                
                record_dict = {
                    "id": record.id,
                    "resident_id": record.resident_id,
                    "dormitory_id": record.dormitory_id,
                    "bed_number": record.bed_number,
                    "check_in_date": record.check_in_date,
                    "check_out_date": record.check_out_date,
                    "status": record.status,
                    "notes": record.notes,
                    "created_at": record.created_at,
                    "updated_at": record.updated_at,
                    "resident_name": record.resident.name if record.resident else None,
                    "dormitory_name": record.dormitory.name if record.dormitory else None,
                    "department_name": record.resident.department.name if record.resident and record.resident.department else None,
                    "days_stayed": days_stayed
                }
                result.append(RecordResponse(**record_dict))
            
            logger.info(f"获取活跃入住记录成功，共{len(result)}条记录")
            return result
            
        except Exception as e:
            logger.error(f"获取活跃入住记录失败: {str(e)}")
            raise
    
    def _calculate_days_stayed(self, check_in_date: date, check_out_date: Optional[date]) -> int:
        """计算已住天数"""
        if check_out_date:
            return (check_out_date - check_in_date).days + 1
        else:
            return (date.today() - check_in_date).days + 1
