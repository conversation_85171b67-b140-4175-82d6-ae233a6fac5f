"""
住户服务层
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.resident import Resident
from app.repositories.resident_repo import ResidentRepository
from app.repositories.department_repo import DepartmentRepository
from app.schemas.resident import ResidentCreate, ResidentUpdate, ResidentResponse
from app.core.logging import logger


class ResidentService:
    """住户服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.repo = ResidentRepository(db)
        self.dept_repo = DepartmentRepository(db)
    
    def get_residents(
        self, 
        skip: int = 0, 
        limit: int = 100,
        department_id: str = None,
        is_active: bool = None,
        keyword: str = None
    ) -> List[ResidentResponse]:
        """获取住户列表"""
        try:
            if keyword:
                residents = self.repo.search_residents(keyword, department_id)
            elif department_id:
                residents = self.repo.get_residents_by_department(department_id)
            else:
                residents = self.repo.get_residents_with_details(skip, limit)
            
            # 过滤活跃状态
            if is_active is not None:
                residents = [r for r in residents if r.is_active == is_active]
            
            # 转换为响应模式
            result = []
            for resident in residents:
                # 获取当前住宿信息
                current_record = self.repo.get_current_residence(resident.id)
                
                # 手动构建字典，然后创建Pydantic模型
                resident_dict = {
                    "id": resident.id,
                    "name": resident.name,
                    "employee_id": resident.employee_id,
                    "phone": resident.phone,
                    "email": resident.email,
                    "department_id": resident.department_id,
                    "project_group": resident.project_group,
                    "is_active": resident.is_active,
                    "created_at": resident.created_at,
                    "updated_at": resident.updated_at,
                    "department_name": resident.department.name if resident.department else None,
                    "current_dormitory": current_record.dormitory.name if current_record and current_record.dormitory else None,
                    "current_bed_number": current_record.bed_number if current_record else None
                }
                result.append(ResidentResponse(**resident_dict))
            
            logger.info(f"获取住户列表成功，共{len(result)}条记录")
            return result
            
        except Exception as e:
            logger.error(f"获取住户列表失败: {str(e)}")
            raise
    
    def get_resident(self, resident_id: str) -> Optional[ResidentResponse]:
        """获取单个住户信息"""
        try:
            resident = self.repo.get_by_id(resident_id)
            if not resident:
                return None
            
            # 获取当前住宿信息
            current_record = self.repo.get_current_residence(resident.id)
            
            # 手动构建字典
            resident_dict = {
                "id": resident.id,
                "name": resident.name,
                "employee_id": resident.employee_id,
                "phone": resident.phone,
                "email": resident.email,
                "department_id": resident.department_id,
                "project_group": resident.project_group,
                "is_active": resident.is_active,
                "created_at": resident.created_at,
                "updated_at": resident.updated_at,
                "department_name": resident.department.name if resident.department else None,
                "current_dormitory": current_record.dormitory.name if current_record and current_record.dormitory else None,
                "current_bed_number": current_record.bed_number if current_record else None
            }
            
            logger.info(f"获取住户信息成功: {resident.name}")
            return ResidentResponse(**resident_dict)
            
        except Exception as e:
            logger.error(f"获取住户信息失败: {str(e)}")
            raise
    
    def create_resident(self, resident_data: ResidentCreate) -> ResidentResponse:
        """创建住户"""
        try:
            # 验证部门是否存在
            department = self.dept_repo.get_by_id(resident_data.department_id)
            if not department:
                raise ValueError("指定的部门不存在")
            
            # 检查员工号是否已存在
            if resident_data.employee_id and self.repo.check_employee_id_exists(resident_data.employee_id):
                raise ValueError("员工号已存在")
            
            # 创建住户
            resident = self.repo.create(resident_data.model_dump())
            
            # 手动构建字典
            resident_dict = {
                "id": resident.id,
                "name": resident.name,
                "employee_id": resident.employee_id,
                "phone": resident.phone,
                "email": resident.email,
                "department_id": resident.department_id,
                "project_group": resident.project_group,
                "is_active": resident.is_active,
                "created_at": resident.created_at,
                "updated_at": resident.updated_at,
                "department_name": department.name,
                "current_dormitory": None,
                "current_bed_number": None
            }
            result = ResidentResponse(**resident_dict)
            
            logger.info(f"创建住户成功: {resident.name}")
            return result
            
        except Exception as e:
            logger.error(f"创建住户失败: {str(e)}")
            raise
    
    def update_resident(self, resident_id: str, resident_data: ResidentUpdate) -> Optional[ResidentResponse]:
        """更新住户信息"""
        try:
            resident = self.repo.get_by_id(resident_id)
            if not resident:
                return None

            # 验证部门是否存在
            if resident_data.department_id:
                department = self.dept_repo.get_by_id(resident_data.department_id)
                if not department:
                    raise ValueError("指定的部门不存在")
            
            # 检查员工号是否已存在
            if resident_data.employee_id and self.repo.check_employee_id_exists(
                resident_data.employee_id, resident_id
            ):
                raise ValueError("员工号已存在")
            
            # 更新住户
            update_data = {k: v for k, v in resident_data.model_dump().items() if v is not None}
            updated_resident = self.repo.update(resident_id, update_data)
            
            # 获取当前住宿信息
            current_record = self.repo.get_current_residence(updated_resident.id)
            
            # 手动构建字典
            resident_dict = {
                "id": updated_resident.id,
                "name": updated_resident.name,
                "employee_id": updated_resident.employee_id,
                "phone": updated_resident.phone,
                "email": updated_resident.email,
                "department_id": updated_resident.department_id,
                "project_group": updated_resident.project_group,
                "is_active": updated_resident.is_active,
                "created_at": updated_resident.created_at,
                "updated_at": updated_resident.updated_at,
                "department_name": updated_resident.department.name if updated_resident.department else None,
                "current_dormitory": current_record.dormitory.name if current_record and current_record.dormitory else None,
                "current_bed_number": current_record.bed_number if current_record else None
            }
            result = ResidentResponse(**resident_dict)
            
            logger.info(f"更新住户成功: {updated_resident.name}")
            return result
            
        except Exception as e:
            logger.error(f"更新住户失败: {str(e)}")
            raise
    
    def delete_resident(self, resident_id: str) -> bool:
        """删除住户"""
        try:
            resident = self.repo.get_by_id(resident_id)
            if not resident:
                return False
            
            # 检查是否有活跃的入住记录
            current_record = self.repo.get_current_residence(resident_id)
            if current_record:
                raise ValueError("住户当前有活跃的入住记录，无法删除")
            
            # 删除住户
            success = self.repo.delete(resident_id)
            
            if success:
                logger.info(f"删除住户成功: {resident.name}")
            
            return success
            
        except Exception as e:
            logger.error(f"删除住户失败: {str(e)}")
            raise
    
    def get_residents_by_department(self, department_id: str) -> List[ResidentResponse]:
        """根据部门获取住户列表"""
        try:
            residents = self.repo.get_residents_by_department(department_id)
            
            result = []
            for resident in residents:
                current_record = self.repo.get_current_residence(resident.id)
                
                resident_dict = {
                    "id": resident.id,
                    "name": resident.name,
                    "employee_id": resident.employee_id,
                    "phone": resident.phone,
                    "email": resident.email,
                    "department_id": resident.department_id,
                    "is_active": resident.is_active,
                    "created_at": resident.created_at,
                    "updated_at": resident.updated_at,
                    "department_name": resident.department.name if resident.department else None,
                    "current_dormitory": current_record.dormitory.name if current_record and current_record.dormitory else None,
                    "current_bed_number": current_record.bed_number if current_record else None
                }
                result.append(ResidentResponse(**resident_dict))
            
            logger.info(f"根据部门获取住户列表成功，共{len(result)}条记录")
            return result
            
        except Exception as e:
            logger.error(f"根据部门获取住户列表失败: {str(e)}")
            raise
