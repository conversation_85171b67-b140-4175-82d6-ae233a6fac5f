"""
入住记录相关模式
"""
from typing import Optional
from datetime import date
from pydantic import BaseModel, Field
from .base import BaseSchema, TimestampMixin, IDMixin


class RecordBase(BaseModel):
    """入住记录基础模式"""
    resident_id: str = Field(..., description="住户ID")
    dormitory_id: str = Field(..., description="宿舍ID")
    bed_number: int = Field(..., gt=0, description="床位号")
    check_in_date: date = Field(..., description="入住日期")
    check_out_date: Optional[date] = Field(None, description="离开日期")
    status: str = Field(default="ACTIVE", description="状态")
    notes: Optional[str] = Field(None, max_length=500, description="备注")


class RecordCreate(BaseModel):
    """创建入住记录模式"""
    resident_id: str = Field(..., description="住户ID")
    dormitory_id: str = Field(..., description="宿舍ID")
    bed_number: Optional[int] = Field(None, gt=0, description="床位号（可选，系统自动分配）")
    check_in_date: date = Field(..., description="入住日期")
    notes: Optional[str] = Field(None, max_length=500, description="备注")


class RecordUpdate(BaseModel):
    """更新入住记录模式"""
    check_out_date: Optional[date] = Field(None, description="离开日期")
    status: Optional[str] = Field(None, description="状态")
    notes: Optional[str] = Field(None, max_length=500, description="备注")


class RecordResponse(RecordBase, IDMixin, TimestampMixin):
    """入住记录响应模式"""
    resident_name: Optional[str] = Field(None, description="住户姓名")
    dormitory_name: Optional[str] = Field(None, description="宿舍名称")
    department_name: Optional[str] = Field(None, description="部门名称")
    project_group: Optional[str] = Field(None, description="项目组")
    days_stayed: Optional[int] = Field(None, description="已住天数")


class RecordFilter(BaseModel):
    """入住记录筛选条件"""
    dormitory_id: Optional[str] = Field(None, description="宿舍ID筛选")
    department_id: Optional[str] = Field(None, description="部门ID筛选")
    status: Optional[str] = Field(None, description="状态筛选")
    check_in_start: Optional[date] = Field(None, description="入住开始日期")
    check_in_end: Optional[date] = Field(None, description="入住结束日期")
