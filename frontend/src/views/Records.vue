<template>
  <div class="records">
    <div class="app-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">入住记录</h1>
        <div class="page-actions">
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新增记录
          </el-button>
          <el-button type="success" @click="showActiveRecords">
            <el-icon><View /></el-icon>
            活跃记录
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 搜索筛选 -->
      <div class="app-card">
        <el-form :model="searchForm" inline>
          <el-form-item label="宿舍">
            <el-select
              v-model="searchForm.dormitory_id"
              placeholder="请选择宿舍"
              clearable
              style="width: 180px"
            >
              <el-option
                v-for="dorm in dormitoryStore.dormitories"
                :key="dorm.id"
                :label="dorm.name"
                :value="dorm.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="部门">
            <el-select
              v-model="searchForm.department_id"
              placeholder="请选择部门"
              clearable
              style="width: 180px"
            >
              <el-option
                v-for="dept in departmentStore.departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="入住中" value="ACTIVE" />
              <el-option label="已离开" value="COMPLETED" />
              <el-option label="已取消" value="CANCELLED" />
            </el-select>
          </el-form-item>
          <el-form-item label="入住日期">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="app-card">
        <el-table
          v-loading="recordStore.loading"
          :data="filteredRecords"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="resident_name" label="住户姓名" width="120" />
          <el-table-column prop="department_name" label="所属部门" width="120" />
          <el-table-column prop="dormitory_name" label="宿舍" width="120" />
          <el-table-column prop="bed_number" label="床位号" width="80" />
          <el-table-column prop="check_in_date" label="入住日期" width="120" />
          <el-table-column prop="check_out_date" label="离开日期" width="120">
            <template #default="{ row }">
              {{ row.check_out_date || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="days_stayed" label="住宿天数" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="row.status === 'ACTIVE' ? 'success' :
                       row.status === 'COMPLETED' ? 'info' : 'warning'"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="notes" label="备注" min-width="150">
            <template #default="{ row }">
              {{ row.notes || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="row.status === 'ACTIVE'"
                type="warning"
                size="small"
                @click="showCheckoutDialog(row)"
              >
                办理离开
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="showEditDialog(row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 新增/编辑对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="isEdit ? '编辑入住记录' : '新增入住记录'"
        width="600px"
        @close="resetForm"
      >
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="住户" prop="resident_id">
                <el-select
                  v-model="formData.resident_id"
                  placeholder="请选择住户"
                  filterable
                  style="width: 100%"
                  @change="onResidentChange"
                >
                  <el-option
                    v-for="resident in availableResidents"
                    :key="resident.id"
                    :label="`${resident.name} (${resident.employee_id || '无员工号'})`"
                    :value="resident.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="宿舍" prop="dormitory_id">
                <el-select
                  v-model="formData.dormitory_id"
                  placeholder="请选择宿舍"
                  style="width: 100%"
                  @change="onDormitoryChange"
                >
                  <el-option
                    v-for="dorm in availableDormitories"
                    :key="dorm.id"
                    :label="`${dorm.name} (${dorm.available_beds || dorm.total_beds}/${dorm.total_beds})`"
                    :value="dorm.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="床位号">
                <el-input-number
                  v-model="formData.bed_number"
                  :min="1"
                  :max="selectedDormitory?.total_beds || 20"
                  placeholder="留空自动分配"
                  style="width: 100%"
                  clearable
                />
                <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                  留空时系统自动分配可用床位
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="入住日期" prop="check_in_date">
                <el-date-picker
                  v-model="formData.check_in_date"
                  type="date"
                  placeholder="请选择入住日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="备注" prop="notes">
            <el-input
              v-model="formData.notes"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>

        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </template>
      </el-dialog>

      <!-- 办理离开对话框 -->
      <el-dialog
        v-model="checkoutVisible"
        title="办理离开"
        width="500px"
      >
        <div v-if="currentRecord">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="住户姓名">{{ currentRecord.resident_name }}</el-descriptions-item>
            <el-descriptions-item label="宿舍">{{ currentRecord.dormitory_name }}</el-descriptions-item>
            <el-descriptions-item label="床位号">{{ currentRecord.bed_number }}号床</el-descriptions-item>
            <el-descriptions-item label="入住日期">{{ currentRecord.check_in_date }}</el-descriptions-item>
            <el-descriptions-item label="已住天数">{{ currentRecord.days_stayed }}天</el-descriptions-item>
          </el-descriptions>

          <el-form
            ref="checkoutFormRef"
            :model="checkoutForm"
            :rules="checkoutRules"
            label-width="100px"
            style="margin-top: 20px"
          >
            <el-form-item label="离开日期" prop="checkout_date">
              <el-date-picker
                v-model="checkoutForm.checkout_date"
                type="date"
                placeholder="请选择离开日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="备注" prop="notes">
              <el-input
                v-model="checkoutForm.notes"
                type="textarea"
                :rows="3"
                placeholder="请输入离开备注"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </div>

        <template #footer>
          <el-button @click="checkoutVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="checkoutLoading"
            @click="handleCheckout"
          >
            确认离开
          </el-button>
        </template>
      </el-dialog>

      <!-- 活跃记录对话框 -->
      <el-dialog
        v-model="activeVisible"
        title="当前活跃入住记录"
        width="1000px"
      >
        <el-table
          :data="recordStore.activeRecords"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="resident_name" label="住户姓名" width="120" />
          <el-table-column prop="department_name" label="所属部门" width="120" />
          <el-table-column prop="dormitory_name" label="宿舍" width="120" />
          <el-table-column prop="bed_number" label="床位号" width="80" />
          <el-table-column prop="check_in_date" label="入住日期" width="120" />
          <el-table-column prop="days_stayed" label="已住天数" width="100" />
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button
                type="warning"
                size="small"
                @click="showCheckoutDialog(row)"
              >
                办理离开
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessageBox } from 'element-plus'
import {
  Plus,
  View,
  Refresh,
  Search,
  RefreshLeft
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

import { useRecordStore } from '@/stores/records'
import { useResidentStore } from '@/stores/residents'
import { useDormitoryStore } from '@/stores/dormitories'
import { useDepartmentStore } from '@/stores/departments'

// 使用stores
const recordStore = useRecordStore()
const residentStore = useResidentStore()
const dormitoryStore = useDormitoryStore()
const departmentStore = useDepartmentStore()

// 响应式数据
const dialogVisible = ref(false)
const checkoutVisible = ref(false)
const activeVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const checkoutLoading = ref(false)
const formRef = ref()
const checkoutFormRef = ref()
const currentRecord = ref(null)
const dateRange = ref([])

// 搜索表单
const searchForm = ref({
  dormitory_id: null,
  department_id: null,
  status: null
})

// 表单数据
const formData = ref({
  resident_id: '',
  dormitory_id: '',
  bed_number: null,
  check_in_date: '',
  notes: ''
})

// 离开表单
const checkoutForm = ref({
  checkout_date: '',
  notes: ''
})

// 表单验证规则
const formRules = {
  resident_id: [
    { required: true, message: '请选择住户', trigger: 'change' }
  ],
  dormitory_id: [
    { required: true, message: '请选择宿舍', trigger: 'change' }
  ],
  check_in_date: [
    { required: true, message: '请选择入住日期', trigger: 'change' }
  ]
}

const checkoutRules = {
  checkout_date: [
    { required: true, message: '请选择离开日期', trigger: 'change' }
  ]
}

// 计算属性
const filteredRecords = computed(() => {
  let records = recordStore.records

  if (searchForm.value.dormitory_id) {
    records = records.filter(record =>
      record.dormitory_id === searchForm.value.dormitory_id
    )
  }

  if (searchForm.value.department_id) {
    records = records.filter(record =>
      record.department_name &&
      departmentStore.departments.find(d =>
        d.id === searchForm.value.department_id && d.name === record.department_name
      )
    )
  }

  if (searchForm.value.status) {
    records = records.filter(record =>
      record.status === searchForm.value.status
    )
  }

  if (dateRange.value && dateRange.value.length === 2) {
    const [start, end] = dateRange.value
    records = records.filter(record =>
      record.check_in_date >= start && record.check_in_date <= end
    )
  }

  return records
})

const availableResidents = computed(() => {
  // 只显示没有活跃入住记录的住户
  return residentStore.residents.filter(resident =>
    resident.is_active && !resident.current_dormitory
  )
})

const availableDormitories = computed(() => {
  return dormitoryStore.dormitories.filter(dorm => dorm.is_active)
})

const selectedDormitory = computed(() => {
  return dormitoryStore.dormitories.find(dorm =>
    dorm.id === formData.value.dormitory_id
  )
})

// 方法
const refreshData = async () => {
  await Promise.all([
    recordStore.fetchRecords(),
    residentStore.fetchResidents(),
    dormitoryStore.fetchDormitories(),
    departmentStore.fetchDepartments()
  ])
}

const showCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const showEditDialog = (row) => {
  isEdit.value = true
  formData.value = { ...row }
  dialogVisible.value = true
}

const showCheckoutDialog = (row) => {
  currentRecord.value = row
  checkoutForm.value = {
    checkout_date: dayjs().format('YYYY-MM-DD'),
    notes: ''
  }
  checkoutVisible.value = true
}

const showActiveRecords = async () => {
  await recordStore.fetchActiveRecords()
  activeVisible.value = true
}

const resetForm = () => {
  formData.value = {
    resident_id: '',
    dormitory_id: '',
    bed_number: null,
    check_in_date: '',
    notes: ''
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const resetSearch = () => {
  searchForm.value = {
    dormitory_id: null,
    department_id: null,
    status: null
  }
  dateRange.value = []
}

const onResidentChange = (residentId) => {
  // 可以在这里添加住户选择后的逻辑
  console.log('选择住户:', residentId)
}

const onDormitoryChange = (dormitoryId) => {
  // 重置床位号为自动分配
  formData.value.bed_number = null
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    if (isEdit.value) {
      await recordStore.updateRecord(formData.value.id, formData.value)
    } else {
      await recordStore.createRecord(formData.value)
    }

    dialogVisible.value = false
    resetForm()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleCheckout = async () => {
  if (!checkoutFormRef.value) return

  try {
    await checkoutFormRef.value.validate()
    checkoutLoading.value = true

    await recordStore.checkoutResident(
      currentRecord.value.id,
      checkoutForm.value.checkout_date,
      checkoutForm.value.notes
    )

    checkoutVisible.value = false
    activeVisible.value = false
  } catch (error) {
    console.error('办理离开失败:', error)
  } finally {
    checkoutLoading.value = false
  }
}

const handleDelete = async (row) => {
  try {
    // 根据记录状态显示不同的确认信息
    let confirmMessage = `确定要删除"${row.resident_name}"的入住记录吗？此操作不可恢复。`

    if (row.status === 'ACTIVE') {
      confirmMessage = `确定要删除"${row.resident_name}"的活跃入住记录吗？\n删除后该住户将被视为已离开，床位将被释放。\n此操作不可恢复。`
    }

    await ElMessageBox.confirm(
      confirmMessage,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    await recordStore.deleteRecord(row.id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

const disabledDate = (time) => {
  if (!currentRecord.value) return false
  // 离开日期不能早于入住日期
  return time.getTime() < new Date(currentRecord.value.check_in_date).getTime()
}

const getStatusText = (status) => {
  const statusMap = {
    'ACTIVE': '入住中',
    'COMPLETED': '已离开',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.records {
  .app-card {
    margin-bottom: 20px;
  }
}
</style>
