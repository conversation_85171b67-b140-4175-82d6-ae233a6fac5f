#!/usr/bin/env python3
"""
测试修改后的功能
"""
import sys
import os
import asyncio
from datetime import date

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)
os.chdir(project_root)

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models import Resident, Department, ResidenceRecord
from app.services.resident_service import ResidentService
from app.services.record_service import RecordService
from app.schemas.resident import ResidentCreate, ResidentUpdate
from app.schemas.record import RecordCreate
from app.core.logging import get_logger

logger = get_logger(__name__)


def test_resident_project_group():
    """测试住户项目组字段"""
    db = SessionLocal()
    try:
        # 获取一个部门
        department = db.query(Department).first()
        if not department:
            logger.error("没有找到部门数据")
            return False
        
        # 创建住户服务
        resident_service = ResidentService(db)
        
        # 测试创建带项目组的住户
        resident_data = ResidentCreate(
            name="测试用户",
            employee_id="TEST001",
            phone="13800000001",
            email="<EMAIL>",
            department_id=department.id,
            project_group="测试项目组",
            is_active=True
        )
        
        # 创建住户
        created_resident = resident_service.create_resident(resident_data)
        logger.info(f"创建住户成功: {created_resident.name}, 项目组: {created_resident.project_group}")
        
        # 测试更新项目组
        update_data = ResidentUpdate(project_group="更新后的项目组")
        updated_resident = resident_service.update_resident(created_resident.id, update_data)
        logger.info(f"更新住户成功: {updated_resident.name}, 项目组: {updated_resident.project_group}")
        
        # 清理测试数据
        resident_service.delete_resident(created_resident.id)
        logger.info("清理测试数据完成")
        
        return True
        
    except Exception as e:
        logger.error(f"测试住户项目组失败: {str(e)}")
        return False
    finally:
        db.close()


def test_record_project_group_display():
    """测试入住记录中项目组显示"""
    db = SessionLocal()
    try:
        # 获取一个住户和宿舍
        resident = db.query(Resident).first()
        if not resident:
            logger.error("没有找到住户数据")
            return False
        
        # 更新住户的项目组
        resident.project_group = "测试记录项目组"
        db.commit()
        
        # 创建记录服务
        record_service = RecordService(db)
        
        # 获取记录列表，检查是否包含项目组信息
        records = record_service.get_records(limit=10)
        
        for record in records:
            if record.resident_id == resident.id:
                logger.info(f"记录中的项目组: {record.project_group}")
                return True
        
        logger.info("没有找到对应的记录")
        return True
        
    except Exception as e:
        logger.error(f"测试记录项目组显示失败: {str(e)}")
        return False
    finally:
        db.close()


def main():
    """主函数"""
    logger.info("开始测试修改后的功能...")
    
    # 测试住户项目组字段
    if test_resident_project_group():
        logger.info("✓ 住户项目组字段测试通过")
    else:
        logger.error("✗ 住户项目组字段测试失败")
    
    # 测试入住记录中项目组显示
    if test_record_project_group_display():
        logger.info("✓ 入住记录项目组显示测试通过")
    else:
        logger.error("✗ 入住记录项目组显示测试失败")
    
    logger.info("测试完成！")


if __name__ == "__main__":
    main()
