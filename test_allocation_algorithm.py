#!/usr/bin/env python3
"""
测试修正后的月度分摊算法
"""
import sys
import os
from datetime import date, timedelta
from collections import defaultdict

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_monthly_allocation_algorithm():
    """测试月度分摊算法的正确性"""
    print("=== 测试月度分摊算法 ===\n")
    
    # 模拟数据：3天，2个宿舍，3个部门
    print("测试场景：")
    print("- 统计期间：3天")
    print("- 宿舍A：10个床位")
    print("- 宿舍B：8个床位")
    print("- 部门：技术部、销售部、行政部")
    print()
    
    # 模拟每日分摊数据
    daily_data = [
        # 第1天
        {
            "date": "2024-01-01",
            "dormitory_A": {
                "total_beds": 10,
                "departments": {
                    "tech": {"name": "技术部", "ratio": 0.6},      # 6个人
                    "sales": {"name": "销售部", "ratio": 0.4}       # 4个人
                }
            },
            "dormitory_B": {
                "total_beds": 8,
                "departments": {
                    "admin": {"name": "行政部", "ratio": 1.0}       # 8个人
                }
            }
        },
        # 第2天
        {
            "date": "2024-01-02", 
            "dormitory_A": {
                "total_beds": 10,
                "departments": {
                    "tech": {"name": "技术部", "ratio": 0.5},      # 5个人
                    "sales": {"name": "销售部", "ratio": 0.3},     # 3个人
                    "admin": {"name": "行政部", "ratio": 0.2}      # 2个人
                }
            },
            "dormitory_B": {
                "total_beds": 8,
                "departments": {
                    "admin": {"name": "行政部", "ratio": 0.75},    # 6个人
                    "sales": {"name": "销售部", "ratio": 0.25}     # 2个人
                }
            }
        },
        # 第3天
        {
            "date": "2024-01-03",
            "dormitory_A": {
                "total_beds": 10,
                "departments": {
                    "tech": {"name": "技术部", "ratio": 0.7},      # 7个人
                    "sales": {"name": "销售部", "ratio": 0.3}      # 3个人
                }
            },
            "dormitory_B": {
                "total_beds": 8,
                "departments": {
                    "admin": {"name": "行政部", "ratio": 1.0}      # 8个人
                }
            }
        }
    ]
    
    # 计算每日各部门的总分摊比例
    print("每日分摊情况：")
    daily_department_totals = []  # [{date, dept_totals, total_beds}]

    for day_idx, day_data in enumerate(daily_data, 1):
        print(f"\n第{day_idx}天 ({day_data['date']}):")

        # 计算当日总床位数和各部门床位数
        daily_total_beds = 0
        dept_beds = defaultdict(float)

        for dorm_name, dorm_data in day_data.items():
            if dorm_name == "date":
                continue

            dorm_total_beds = dorm_data['total_beds']
            daily_total_beds += dorm_total_beds

            print(f"  {dorm_name} (总床位: {dorm_total_beds}):")
            for dept_id, dept_info in dorm_data["departments"].items():
                ratio = dept_info["ratio"]
                beds = dorm_total_beds * ratio
                dept_beds[dept_id] += beds
                print(f"    {dept_info['name']}: {ratio:.1%} × {dorm_total_beds} = {beds:.1f}床")

        # 计算当日各部门的总分摊比例
        print(f"  当日总床位: {daily_total_beds}")
        daily_dept_ratios = {}
        for dept_id, beds in dept_beds.items():
            ratio = beds / daily_total_beds if daily_total_beds > 0 else 0
            daily_dept_ratios[dept_id] = ratio

            dept_name = None
            for dorm_data in day_data.values():
                if isinstance(dorm_data, dict) and "departments" in dorm_data:
                    if dept_id in dorm_data["departments"]:
                        dept_name = dorm_data["departments"][dept_id]["name"]
                        break

            print(f"  {dept_name}总分摊: {beds:.1f} / {daily_total_beds} = {ratio:.1%}")

        daily_department_totals.append({
            'date': day_data['date'],
            'dept_ratios': daily_dept_ratios,
            'total_beds': daily_total_beds
        })

    # 计算月度平均分摊比例
    print(f"\n月度平均分摊比例计算：")
    total_days = len(daily_data)
    all_dept_ids = set()
    for day_info in daily_department_totals:
        all_dept_ids.update(day_info['dept_ratios'].keys())

    monthly_ratios = {}
    for dept_id in all_dept_ids:
        # 收集该部门每日的分摊比例
        daily_ratios = []
        for day_info in daily_department_totals:
            ratio = day_info['dept_ratios'].get(dept_id, 0.0)
            daily_ratios.append(ratio)

        # 计算月度平均分摊比例 = Σ每日分摊比例 / 统计天数
        monthly_avg_ratio = sum(daily_ratios) / total_days
        monthly_ratios[dept_id] = monthly_avg_ratio

        # 获取部门名称
        dept_name = None
        for day_data in daily_data:
            for dorm_data in day_data.values():
                if isinstance(dorm_data, dict) and "departments" in dorm_data:
                    if dept_id in dorm_data["departments"]:
                        dept_name = dorm_data["departments"][dept_id]["name"]
                        break
            if dept_name:
                break

        print(f"  {dept_name}: ({' + '.join([f'{r:.1%}' for r in daily_ratios])}) / {total_days} = {monthly_avg_ratio:.1%}")

    # 验证总比例
    total_ratio = sum(monthly_ratios.values())
    print(f"\n总分摊比例验证: {total_ratio:.1%}")

    if abs(total_ratio - 1.0) < 0.01:  # 允许小的舍入误差
        print("✓ 总分摊比例正确 (≈100%)")
    else:
        print(f"✗ 总分摊比例错误 (应为100%，实际为{total_ratio:.1%})")
        return False
    
    print(f"\n算法验证:")
    print("✓ 修正后的算法：月度分摊比例 = Σ每日分摊比例 / 统计天数")
    print("✓ 考虑了每日分摊比例的变化")
    print("✓ 适用于当月未结束的情况")
    
    return True

if __name__ == "__main__":
    print("开始测试修正后的月度分摊算法...\n")
    
    success = test_monthly_allocation_algorithm()
    
    if success:
        print("\n🎉 算法测试通过!")
        print("\n修正要点:")
        print("- 原算法：直接累加床位天数")
        print("- 新算法：计算平均分摊比例")
        print("- 公式：月度分摊比例 = Σ每日分摊比例 / 统计天数")
        sys.exit(0)
    else:
        print("\n❌ 算法测试失败!")
        sys.exit(1)
